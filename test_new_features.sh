#!/usr/bin/env zsh
# 测试新功能的脚本

set -euo pipefail

# 导入主脚本的函数（仅测试用）
source daily_update.sh

# 测试函数
test_function() {
  local func_name="$1"
  echo "测试 $func_name..."
  
  # 模拟必要的全局变量
  typeset -gA SCRIPT_STATS
  typeset -gA COMMAND_AVAILABLE
  
  # 检查命令可用性
  COMMAND_AVAILABLE[reflector]=$(command -v reflector &>/dev/null && echo "true" || echo "false")
  COMMAND_AVAILABLE[journalctl]=$(command -v journalctl &>/dev/null && echo "true" || echo "false")
  COMMAND_AVAILABLE[fc-cache]=$(command -v fc-cache &>/dev/null && echo "true" || echo "false")
  
  case "$func_name" in
    "update_mirrors")
      update_mirrors
      echo "镜像源状态: ${SCRIPT_STATS[mirror_status]}"
      ;;
    "cleanup_systemd_logs")
      cleanup_systemd_logs
      echo "日志清理状态: ${SCRIPT_STATS[journal_cleanup]}"
      ;;
    "cleanup_gnome_cache")
      cleanup_gnome_cache
      echo "GNOME缓存清理: ${SCRIPT_STATS[gnome_cache]}"
      ;;
    "cleanup_dev_caches")
      cleanup_dev_caches
      echo "开发工具缓存清理: ${SCRIPT_STATS[dev_cache]}"
      ;;
    *)
      echo "未知函数: $func_name"
      return 1
      ;;
  esac
  
  echo "✓ $func_name 测试完成"
  echo
}

# 主测试流程
main() {
  echo "开始测试新功能..."
  echo
  
  # 测试各个新功能
  test_function "update_mirrors"
  test_function "cleanup_systemd_logs" 
  test_function "cleanup_gnome_cache"
  test_function "cleanup_dev_caches"
  
  echo "所有测试完成！"
}

# 如果直接运行此脚本则执行测试
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  main "$@"
fi
