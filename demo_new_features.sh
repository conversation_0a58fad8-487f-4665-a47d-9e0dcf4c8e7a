#!/usr/bin/env zsh
# 演示新功能的脚本（安全模式，不会实际修改系统）

set -euo pipefail

# 颜色定义
if [[ -t 1 ]]; then
  typeset -gr GREEN='\033[0;32m' RED='\033[0;31m' YELLOW='\033[0;33m'
  typeset -gr BLUE='\033[0;34m' CYAN='\033[0;36m' BOLD='\033[1m' NC='\033[0m'
  typeset -gr CHECKMARK='✓' CROSSMARK='✗' GEAR='⚙'
else
  typeset -gr GREEN='' RED='' YELLOW='' BLUE='' CYAN='' BOLD='' NC=''
  typeset -gr CHECKMARK='[OK]' CROSSMARK='[FAIL]' GEAR='[WORK]'
fi

# 日志函数
log_time() { date +'%H:%M'; }
log() { echo -e "${BLUE}[$(log_time)] $*${NC}"; }
log_success() { echo -e "${GREEN}[$(log_time)] ${CHECKMARK} $*${NC}"; }
log_warning() { echo -e "${YELLOW}[$(log_time)] ⚠ $*${NC}"; }

# 全局变量
typeset -gA COMMAND_AVAILABLE
typeset -gA DEMO_STATS

# 检查命令可用性
check_commands() {
  local commands=("reflector" "journalctl" "fc-cache" "npm" "pip" "docker")
  for cmd in "${commands[@]}"; do
    if command -v "$cmd" &>/dev/null; then
      COMMAND_AVAILABLE[$cmd]=true
    else
      COMMAND_AVAILABLE[$cmd]=false
    fi
  done
}

# 演示镜像源检查
demo_update_mirrors() {
  log "演示：检查镜像源优化功能"
  
  if [[ "${COMMAND_AVAILABLE[reflector]}" == "true" ]]; then
    log "✓ reflector 已安装"
    log "模拟执行: reflector --country China,Japan,Korea --age 12 --protocol https --sort rate"
    log "这将选择最快的中国、日本、韩国镜像源"
    DEMO_STATS[mirror_status]="可用"
  else
    log_warning "reflector 未安装，建议安装: sudo pacman -S reflector"
    DEMO_STATS[mirror_status]="需要安装 reflector"
  fi
  log_success "镜像源优化功能检查完成"
  echo
}

# 演示日志清理
demo_cleanup_systemd_logs() {
  log "演示：检查 systemd 日志清理功能"
  
  if [[ "${COMMAND_AVAILABLE[journalctl]}" == "true" ]]; then
    log "✓ journalctl 已安装"
    local log_size=$(journalctl --disk-usage 2>/dev/null | grep -oE '[0-9.]+[KMGT]B' || echo "未知")
    log "当前日志占用空间: $log_size"
    log "模拟执行: journalctl --vacuum-time=7d"
    log "这将清理 7 天前的日志"
    DEMO_STATS[journal_cleanup]="当前: $log_size"
  else
    log_warning "journalctl 未找到"
    DEMO_STATS[journal_cleanup]="不可用"
  fi
  log_success "systemd 日志清理功能检查完成"
  echo
}

# 演示 GNOME 缓存清理
demo_cleanup_gnome_cache() {
  log "演示：检查 GNOME 缓存清理功能"
  
  local gnome_dirs=(
    "$HOME/.cache/gnome-software"
    "$HOME/.cache/gnome-shell" 
    "$HOME/.cache/gstreamer-1.0"
    "$HOME/.cache/tracker"
    "$HOME/.cache/evolution"
  )
  
  local total_size=0
  local found_dirs=0
  
  for dir in "${gnome_dirs[@]}"; do
    if [[ -d "$dir" ]]; then
      local dir_size=$(du -sb "$dir" 2>/dev/null | cut -f1 || echo 0)
      total_size=$((total_size + dir_size))
      found_dirs=$((found_dirs + 1))
      log "找到缓存目录: $(basename "$dir") ($(numfmt --to=iec $dir_size))"
    fi
  done
  
  if (( found_dirs > 0 )); then
    local total_mb=$((total_size / 1024 / 1024))
    log "GNOME 缓存总大小: ${total_mb}MB"
    DEMO_STATS[gnome_cache]="${total_mb}MB 可清理"
  else
    log "未找到 GNOME 缓存目录"
    DEMO_STATS[gnome_cache]="无需清理"
  fi
  log_success "GNOME 缓存清理功能检查完成"
  echo
}

# 演示开发工具缓存清理
demo_cleanup_dev_caches() {
  log "演示：检查开发工具缓存清理功能"
  
  local available_tools=()
  
  # 检查各种开发工具
  if [[ "${COMMAND_AVAILABLE[npm]}" == "true" ]]; then
    available_tools+=("npm")
    log "✓ npm 已安装，可清理缓存"
  fi
  
  if command -v yarn &>/dev/null; then
    available_tools+=("yarn")
    log "✓ yarn 已安装，可清理缓存"
  fi
  
  if [[ "${COMMAND_AVAILABLE[pip]}" == "true" ]]; then
    available_tools+=("pip")
    log "✓ pip 已安装，可清理缓存"
  fi
  
  if command -v cargo &>/dev/null; then
    available_tools+=("cargo")
    log "✓ cargo 已安装，可清理缓存"
  fi
  
  if [[ "${COMMAND_AVAILABLE[docker]}" == "true" ]] && systemctl is-active docker &>/dev/null; then
    available_tools+=("docker")
    log "✓ docker 运行中，可清理系统缓存"
  fi
  
  # 检查 Maven 和 Gradle 目录
  if [[ -d "$HOME/.m2/repository" ]]; then
    local maven_size=$(du -sb "$HOME/.m2/repository" 2>/dev/null | cut -f1 || echo 0)
    local maven_gb=$((maven_size / 1024 / 1024 / 1024))
    if (( maven_gb > 0 )); then
      available_tools+=("maven")
      log "✓ Maven 仓库: ${maven_gb}GB"
    fi
  fi
  
  if [[ -d "$HOME/.gradle/caches" ]]; then
    local gradle_size=$(du -sb "$HOME/.gradle/caches" 2>/dev/null | cut -f1 || echo 0)
    local gradle_gb=$((gradle_size / 1024 / 1024 / 1024))
    if (( gradle_gb > 0 )); then
      available_tools+=("gradle")
      log "✓ Gradle 缓存: ${gradle_gb}GB"
    fi
  fi
  
  if (( ${#available_tools[@]} > 0 )); then
    DEMO_STATS[dev_cache]="可清理: ${available_tools[*]}"
    log_success "发现 ${#available_tools[@]} 个开发工具可清理缓存"
  else
    DEMO_STATS[dev_cache]="无需清理"
    log "未发现需要清理的开发工具缓存"
  fi
  echo
}

# 演示 paru 优化
demo_paru_optimization() {
  log "演示：paru 优化配置"
  
  if command -v paru &>/dev/null; then
    log "✓ paru 已安装"
    log "优化后的更新命令:"
    log "  paru -Syu --noconfirm --needed --skipreview --removemake --cleanafter"
    log ""
    log "新增参数说明:"
    log "  --removemake  : 自动移除构建依赖"
    log "  --cleanafter  : 构建后自动清理"
    log ""
    log "优势:"
    log "  • 减少磁盘空间占用"
    log "  • 避免构建依赖污染系统"
    log "  • 保持系统整洁"
    DEMO_STATS[paru_optimization]="已优化"
  else
    log_warning "paru 未安装"
    DEMO_STATS[paru_optimization]="需要安装 paru"
  fi
  log_success "paru 优化配置检查完成"
  echo
}

# 显示演示摘要
show_demo_summary() {
  echo -e "${BOLD}${CYAN}╭──────────────────────────────────────────────────────────────╮${NC}"
  echo -e "${BOLD}${CYAN}│                    📊 新功能演示摘要                          │${NC}"
  echo -e "${BOLD}${CYAN}├──────────────────────────────────────────────────────────────┤${NC}"
  echo -e "${CYAN}│${NC} 🔄 系统优化功能                                              ${CYAN}│${NC}"
  echo -e "${CYAN}├──────────────────────────────────────────────────────────────┤${NC}"
  printf "${CYAN}│${NC} %-20s %s${CYAN}│${NC}\n" "镜像源优化:" "${DEMO_STATS[mirror_status]}"
  printf "${CYAN}│${NC} %-20s %s${CYAN}│${NC}\n" "Systemd日志:" "${DEMO_STATS[journal_cleanup]}"
  printf "${CYAN}│${NC} %-20s %s${CYAN}│${NC}\n" "GNOME缓存:" "${DEMO_STATS[gnome_cache]}"
  printf "${CYAN}│${NC} %-20s %s${CYAN}│${NC}\n" "开发工具缓存:" "${DEMO_STATS[dev_cache]}"
  printf "${CYAN}│${NC} %-20s %s${CYAN}│${NC}\n" "Paru优化:" "${DEMO_STATS[paru_optimization]}"
  echo -e "${CYAN}╰──────────────────────────────────────────────────────────────╯${NC}"
  echo
}

# 主函数
main() {
  echo -e "${BOLD}${CYAN}╭──────────────────────────────────────────────────────────────╮${NC}"
  echo -e "${BOLD}${CYAN}│                🚀 Arch Linux 新功能演示                      │${NC}"
  echo -e "${BOLD}${CYAN}╰──────────────────────────────────────────────────────────────╯${NC}"
  echo
  
  # 初始化
  check_commands
  
  # 演示各项新功能
  demo_update_mirrors
  demo_cleanup_systemd_logs
  demo_cleanup_gnome_cache
  demo_cleanup_dev_caches
  demo_paru_optimization
  
  # 显示摘要
  show_demo_summary
  
  log_success "所有新功能演示完成！"
  echo
  log "要使用这些功能，请运行: ./daily_update.sh"
}

main "$@"
