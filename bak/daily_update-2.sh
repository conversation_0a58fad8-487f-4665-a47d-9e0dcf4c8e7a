#!/usr/bin/env zsh
# ==============================================================================
#
#          FILE: arch-update.sh
#
#         USAGE: ./arch-update.sh [options]
#
#   DESCRIPTION: 一个用于 Arch Linux 的自动化每日更新与维护脚本。
#                它负责系统更新、缓存清理、孤儿包移除以及其他组件的维护。
#
#       OPTIONS: ---
#  REQUIREMENTS: zsh, paru, sudo, and other optional tools like flatpak, xmake.
#          BUGS: ---
#         NOTES: ---
#        AUTHOR: ---
#  ORGANIZATION: ---
#       CREATED: ---
#      REVISION: 2.0 (Annotated Version)
#
# ==============================================================================

# 脚本严格模式，确保代码的健壮性和可预测性
# -e: 命令失败时立即退出
# -u: 使用未定义的变量时报错
# -o pipefail: 管道中的任何命令失败，整个管道都视为失败
set -euo pipefail

# ========================
# 常量与全局配置
# ========================
# 使用 typeset -gr (global readonly) 定义全局只读常量，防止在脚本中被意外修改。
typeset -gr PACMAN_LOCK="/var/lib/pacman/db.lck"    # Pacman 锁文件路径，用于检测更新进程是否在运行。
typeset -gr ICON_SCRIPT="$HOME/APP/Scripts/fix_app_icons/fix_app_icons.sh" # 图标修复脚本路径。
typeset -gr PACCACHE_KEEP=2                         # 使用 paccache 清理时，为每个包保留的最近版本数。
typeset -gr ORPHAN_CLEANUP_DAY="Mon"                # 每周一执行孤儿包清理。
typeset -gr PARU_CACHE_DIR="$HOME/.cache/paru/clone" # Paru (AUR 助手) 的构建缓存目录。

# ========================
# 命令行参数解析
# ========================
# 默认值
VERBOSE_MODE=true           # 详细模式，输出所有日志信息。
PARALLEL_MODE=true          # 并行模式，同时更新 flatpak, xmake 等组件以节省时间。
ENABLE_SYSTEM_CLEANUP=true  # 启用额外的系统清理（如缩略图缓存等）。

# 解析传入脚本的命令行参数。
while [[ $# -gt 0 ]]; do
  case $1 in
    -q|--quiet) VERBOSE_MODE=false; shift ;;
    --no-parallel) PARALLEL_MODE=false; shift ;;
    --no-cleanup) ENABLE_SYSTEM_CLEANUP=false; shift ;;
    -h|--help)
      # 显示帮助信息并退出。
      echo "用法: $0 [选项]"
      echo "选项:"
      echo "  -q, --quiet      简洁模式输出"
      echo "  --no-parallel    禁用并行处理"
      echo "  --no-cleanup     禁用系统清理"
      echo "  -h, --help       显示此帮助信息"
      exit 0 ;;
    *) echo "未知选项: $1"; exit 1 ;;
  esac
done

# ========================
# 全局变量
# ========================
typeset -gA SCRIPT_STATS      # 全局关联数组，用于存储脚本运行的统计数据，如耗时、错误数等。
typeset -gA COMMAND_AVAILABLE # 全局关联数组，用于缓存命令是否存在的结果，避免重复检查。
typeset -g temp_dir           # 全局临时目录，用于在并行子进程之间传递状态信息。

# ========================
# 颜色定义与日志函数
# ========================
# 检测标准输出是否连接到终端，如果是，则启用颜色输出。
if [[ -t 1 ]]; then
  typeset -gr GREEN='\033[0;32m' RED='\033[0;31m' YELLOW='\033[0;33m'
  typeset -gr BLUE='\033[0;34m' CYAN='\033[0;36m' BOLD='\033[1m' NC='\033[0m'
  typeset -gr CHECKMARK='✓' CROSSMARK='✗' ARROW='→' GEAR='⚙'
else
  # 如果不是终端（例如在 cron 任务中或重定向到文件），则禁用颜色和特殊字符。
  typeset -gr GREEN='' RED='' YELLOW='' BLUE='' CYAN='' BOLD='' NC=''
  typeset -gr CHECKMARK='[OK]' CROSSMARK='[FAIL]' ARROW='->' GEAR='[WORK]'
fi

CURRENT_STEP=0
log_time() { date +'%H:%M'; } # 获取当前时间戳。
show_progress() { # 显示当前进度的标题。
  CURRENT_STEP=$((CURRENT_STEP + 1))
  printf "${BOLD}${CYAN}[%2d/%d]${NC} ${GEAR} %s\n" "$CURRENT_STEP" "$TOTAL_STEPS" "$1"
}
log() { [[ "$VERBOSE_MODE" == "true" ]] && echo -e "${BLUE}[$(log_time)] $*${NC}"; } # 普通日志，仅在详细模式下显示。
log_success() { echo -e "${GREEN}[$(log_time)] ${CHECKMARK} $*${NC}"; } # 成功日志。
log_warning() { # 警告日志，并增加警告计数。
  (( SCRIPT_STATS[warnings]++ ))
  echo -e "${YELLOW}[$(log_time)] ⚠ $*${NC}"
}
log_error() { # 错误日志，并增加错误计数，输出到 stderr。
  (( SCRIPT_STATS[errors]++ ))
  echo -e "${RED}[$(log_time)] ${CROSSMARK} $*${NC}" >&2
}

# ========================
# 核心功能函数
# ========================

# 以 root 权限安全地运行命令。
# 此函数只应用于那些 **必须** 以 root 身份运行的命令，如 pacman、paccache。
run_sudo() {
  # `sudo -nv` 检查当前用户是否已有有效的 sudo 时间戳，避免不必要的密码提示。
  # 如果没有，`sudo -v` 会请求密码，从而刷新时间戳。
  sudo -nv 2>/dev/null || sudo -v
  sudo "$@"
}

# 检查所有必需和可选的命令是否存在。
check_commands() {
  # `paru` 和 `bc` 是关键依赖。
  local commands=("fastfetch" "flatpak" "xmake" "zinit" "go" "curl" "paccache" "bc" "paru")
  for cmd in "${commands[@]}"; do
    if command -v "$cmd" &>/dev/null; then
      COMMAND_AVAILABLE[$cmd]=true
    else
      COMMAND_AVAILABLE[$cmd]=false
      # 如果关键命令缺失，脚本无法继续，直接报错退出。
      if [[ "$cmd" == "bc" || "$cmd" == "paru" ]]; then
        log_error "关键命令 '$cmd' 未找到。请安装它。"
        return 1
      fi
    fi
  done
  return 0
}

# 检查网络连接。
# 尝试访问多个由不同大公司维护的高可用性端点，以提高检查的可靠性。
check_network() {
  local endpoints=("http://connectivitycheck.gstatic.com/generate_204" "http://www.msftconnecttest.com/connecttest.txt" "http://detectportal.firefox.com/success.txt")
  for endpoint in "${endpoints[@]}"; do
    if curl -Is --connect-timeout 3 --max-time 5 "$endpoint" &>/dev/null; then return 0; fi
  done
  return 1
}

# 带重试机制的命令执行函数。
# 对于网络不稳等情况非常有用。
# 用法: run_with_retry <最大重试次数> <重试间隔秒数> <命令> [命令参数...]
run_with_retry() {
  (( $# < 3 )) && { log_error "run_with_retry 使用错误"; return 1; }
  local max_retries=$1; local retry_delay=$2; shift 2; local cmd=("$@")
  for attempt in {1..$max_retries}; do
    if "${cmd[@]}"; then return 0; fi # 命令成功，返回0
    log_warning "命令 '${cmd[*]}' 尝试 $attempt/$max_retries 失败..."
    (( attempt < max_retries )) && { log_warning "将在 ${retry_delay} 秒后重试"; sleep "$retry_delay"; }
  done
  log_error "命令 '${cmd[*]}' 最终执行失败"
  return 1
}

# 检查根分区磁盘空间。
check_disk_space() {
  local min_free_gb=2
  local available_kb
  available_kb=$(df --output=avail / | tail -n 1 | tr -d ' ' 2>/dev/null || echo 0)
  local available_gb=$((available_kb / 1024 / 1024))
  if (( available_gb < min_free_gb )); then
    log_warning "磁盘空间不足: ${available_gb}GB 可用 (建议至少 ${min_free_gb}GB)"
    return 1 # 返回1表示警告，但不终止脚本
  fi
  log "磁盘空间充足: ${available_gb}GB 可用"
  return 0
}

# 并行执行多个函数。
# 将每个函数作为后台任务(&)启动，并记录其PID，然后使用 wait 等待所有任务完成。
run_parallel() {
  local pids=()
  for cmd in "$@"; do
    $cmd &
    pids+=($!)
  done
  local overall_status=0
  for pid in "${pids[@]}"; do
    wait $pid || overall_status=1 # 如果任何一个子进程失败，则将最终状态设为失败。
  done
  return $overall_status
}

# ========================
# 摘要与格式化函数
# ========================
# 根据状态字符串内容返回带颜色的格式化文本。
format_status() {
  case "$1" in
    *"成功"*|*"已执行"*|*"无需清理"*|*"个"*) echo -e "${GREEN}$1${NC}" ;;
    *"失败"*) echo -e "${RED}$1${NC}" ;;
    *) echo -e "${YELLOW}$1${NC}" ;;
  esac
}

# 计算字符串在终端中的显示宽度，正确处理中日韩(CJK)等双字节字符。
# 这对于在终端中对齐文本至关重要。
calculate_display_width() {
  local text="$1"
  # 移除所有 ANSI 颜色代码和 emoji 的变体选择符。
  local clean_text=$(echo "$text" | sed -E 's/\x1b\[[0-9;]*m//g' | sed 's/️//g')
  local width=0
  for ((i=0; i<${#clean_text}; i++)); do
    local char="${clean_text:$i:1}"
    local unicode_val=$(printf "%d" "'$char" 2>/dev/null || echo "0")
    if [[ $unicode_val -gt 127 ]]; then # Unicode 值大于 127 的通常是多字节字符。
      width=$((width + 2))
    else
      width=$((width + 1))
    fi
  done
  echo $width
}

# 打印一行对齐的摘要信息。
print_aligned_row() {
  local content="$1"
  local target_width=60 # 目标总宽度
  local actual_width=$(calculate_display_width "$content")
  local padding=$((target_width - actual_width))
  [[ $padding -lt 0 ]] && padding=0
  local spaces=$(printf "%*s" $padding "")
  echo -e "${CYAN}║${NC} ${content}${spaces} ${CYAN}║${NC}"
}

# 在脚本结束时打印格式化的执行摘要。
print_summary() {
  local end_time=$(date +%s)
  local duration=$((end_time - SCRIPT_STATS[start_time]))
  local minutes=$((duration / 60))
  local seconds=$((duration % 60))

  echo
  echo -e "${BOLD}${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
  echo -e "${BOLD}${CYAN}║                        📊 执行摘要                           ║${NC}"
  echo -e "${BOLD}${CYAN}╠══════════════════════════════════════════════════════════════╣${NC}"
  print_aligned_row "⏱️  总耗时: ${GREEN}${minutes}分${seconds}秒${NC}"
  print_aligned_row "⚠️  警告: ${YELLOW}${SCRIPT_STATS[warnings]}${NC} | ❌ 错误: ${RED}${SCRIPT_STATS[errors]}${NC}"
  echo -e "${CYAN}╠══════════════════════════════════════════════════════════════╣${NC}"
  echo -e "${CYAN}║${NC} 🧹 清理与维护                                                ${CYAN}║${NC}"
  echo -e "${CYAN}╠══════════════════════════════════════════════════════════════╣${NC}"
  print_aligned_row "  孤儿包: $(format_status "${SCRIPT_STATS[orphans_cleaned]}")"
  print_aligned_row "  Pacman缓存: $(format_status "${SCRIPT_STATS[pacman_cache_freed]}")"
  print_aligned_row "  Paru缓存: $(format_status "${SCRIPT_STATS[paru_cache_status]}")"
  print_aligned_row "  系统清理: $(format_status "${SCRIPT_STATS[system_cleanup]}")"
  echo -e "${CYAN}╠══════════════════════════════════════════════════════════════╣${NC}"
  echo -e "${CYAN}║${NC} 🔄 组件更新                                                  ${CYAN}║${NC}"
  echo -e "${CYAN}╠══════════════════════════════════════════════════════════════╣${NC}"
  print_aligned_row "  Flatpak: $(format_status "${SCRIPT_STATS[flatpak_status]}")"
  print_aligned_row "  Xmake: $(format_status "${SCRIPT_STATS[xmake_status]}")"
  print_aligned_row "  Zinit: $(format_status "${SCRIPT_STATS[zinit_status]}")"
  print_aligned_row "  Go清理: $(format_status "${SCRIPT_STATS[go_status]}")"
  print_aligned_row "  图标修复: $(format_status "${SCRIPT_STATS[icon_status]}")"
  echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
  echo
}

# ========================
# 组件更新与清理函数
# ========================
# 这些函数设计为可以并行运行。它们将结果写入临时文件，主进程稍后会读取这些文件。
update_flatpak() {
  if run_with_retry 3 5 flatpak update -y &>/dev/null; then
    echo "成功" > "$temp_dir/flatpak_status"
    log_success "Flatpak更新完成"
  else
    echo "失败" > "$temp_dir/flatpak_status"
    log_warning "Flatpak更新失败"
  fi
}

update_xmake() {
  if run_with_retry 2 5 xmake repo -u &>/dev/null; then
    echo "成功" > "$temp_dir/xmake_status"
    log_success "Xmake更新完成"
  else
    echo "失败" > "$temp_dir/xmake_status"
    log_warning "Xmake更新失败"
  fi
}

update_zinit() {
  # 在一个新的 zsh 进程中运行 zinit，以确保环境干净。
  if zsh -c 'source "/usr/share/zinit/zinit.zsh"; zinit update --parallel 160' &>/dev/null; then
    echo "成功" > "$temp_dir/zinit_status"
    log_success "Zinit更新完成"
  else
    echo "失败" > "$temp_dir/zinit_status"
    log_warning "Zinit更新失败"
  fi
}

cleanup_go() {
  # 步骤 1: 执行标准的 go clean 命令清理官方缓存
  go clean -cache -modcache -testcache -fuzzcache &>/dev/null
  log "Go 标准缓存清理完成"

  # 步骤 2: 检查并清理用户主目录下的空 Go 目录
  # 定义要检查的目录列表，顺序很重要，从最内层到最外层
  local -a dirs_to_prune=("$HOME/go/pkg" "$HOME/go")
  local pruned_count=0

  for dir in "${dirs_to_prune[@]}"; do
    # 检查目录是否存在，并且为空
    # `ls -A` 会列出包括隐藏文件在内的所有内容（不包括 . 和 ..）
    # 如果 `ls -A` 输出为空，则目录为空
    if [[ -d "$dir" ]] && [[ -z "$(ls -A "$dir" 2>/dev/null)" ]]; then
      if rmdir "$dir" 2>/dev/null; then
        log "已移除空的 Go 目录: $dir"
        (( pruned_count++ ))
      else
        # 如果 rmdir 失败（例如权限问题），记录一个警告
        log_warning "尝试移除空目录 $dir 失败"
      fi
    fi
  done

  # 步骤 3: 根据操作结果设置最终的摘要状态
  if (( pruned_count > 0 )); then
    # 如果删除了目录，在摘要中体现出来
    SCRIPT_STATS[go_status]="已执行 (清理了${pruned_count}个空目录)"
  else
    SCRIPT_STATS[go_status]="已执行"
  fi

  log_success "Go 清理流程完成"
}


# ========================
# 模块化的步骤函数
# ========================
# 步骤1：预检查。这是脚本的“守门员”，确保运行环境是安全的。
run_step_pre_checks() {
  show_progress "系统与环境检查"
  if (( EUID == 0 )); then
    log_error "请不要以 root 用户身份直接运行此脚本"; return 1;
  fi; log_success "用户权限检查通过"

  check_commands || return 1
  log_success "系统组件检查完成"

  if [[ "${COMMAND_AVAILABLE[fastfetch]}" == "true" ]]; then
    fastfetch
  else
    log_warning "未找到 fastfetch，跳过系统信息显示"
  fi

  if ! run_with_retry 2 2 check_network; then
    log_error "网络连接异常"; return 1;
  fi; log_success "网络连接正常"

  check_disk_space # 检查磁盘空间，但即使不足也只是警告，不中断脚本。

  if ! sudo -v &>/dev/null; then
    log_error "无法获取sudo权限，请先运行 'sudo -v' 或检查 sudoers 配置"; return 1;
  fi; log_success "sudo权限验证通过"

  return 0
}

# 步骤2：系统更新。这是脚本的核心功能。
run_step_system_update() {
  show_progress "系统更新"
  # 检查是否有其他 pacman/paru 实例在运行。
  if pgrep -x "pacman|paru" >/dev/null; then
    log_error "检测到正在运行的 pacman/paru 进程，为避免冲突已终止脚本。"; return 1;
  # 检查 pacman 锁文件是否存在。
  elif [[ -f $PACMAN_LOCK ]]; then
    log_warning "发现残留的 pacman 锁文件，可能上次更新异常中断，正在尝试清理..."
    # 清理锁文件需要 root 权限，所以这里使用 run_sudo 是正确的。
    if ! run_sudo rm -v "$PACMAN_LOCK"; then
      log_error "锁文件清理失败，请手动删除 $PACMAN_LOCK"; return 1;
    fi
    log_success "锁文件清理完成"
  fi

  log "开始系统更新 (官方仓库 + AUR)..."
  # ======================================================================
  # ▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼ 核心修正 ▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
  #
  # 错误修正：必须以普通用户身份运行 `paru`。
  # AUR 助手（如 paru, yay）的设计是在构建软件包时使用普通用户权限，
  # 仅在最后的安装阶段通过 `sudo` 调用 `pacman` 来获取 root 权限。
  # 直接使用 `sudo paru` 会导致 AUR 包构建失败。
  #
  if ! run_with_retry 3 10 paru -Syu --noconfirm --needed --skipreview; then
  #
  # ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲ 核心修正 ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲
  # ======================================================================
    log_error "系统更新失败"; return 1;
  fi
  log_success "系统更新完成"
  return 0
}

# 步骤3：系统清理。移除不再需要的文件和包。
run_step_cleanup() {
  show_progress "清理与维护"
  # 清理孤儿包（被卸载的包的不再需要的依赖）。
  local orphans
  if [[ "$(date +'%a')" == "$ORPHAN_CLEANUP_DAY" ]]; then
    log "今天是 $ORPHAN_CLEANUP_DAY，执行孤儿包清理"
    # `pacman -Qtdq` 列出所有孤儿包。`|| true` 防止在没有孤儿包时命令失败导致脚本退出。
    orphans=($(pacman -Qtdq 2>/dev/null || true))
    if (( #orphans > 0 )); then
      log_warning "发现 $#orphans 个孤儿包，开始清理..."
      # `pacman -Rns` 需要 root 权限。
      if run_sudo pacman -Rns --noconfirm "${orphans[@]}" &>/dev/null; then
        SCRIPT_STATS[orphans_cleaned]="$#orphans 个"; log_success "孤儿包清理完成"
      else
        SCRIPT_STATS[orphans_cleaned]="清理失败"; log_error "孤儿包清理失败"
      fi
    else
      SCRIPT_STATS[orphans_cleaned]="0 个"; log_success "未发现孤儿包"
    fi
  else
    log "跳过孤儿包清理（非预定日）"; SCRIPT_STATS[orphans_cleaned]="未执行"
  fi

  # 清理 pacman 缓存。
  log "清理pacman包缓存（保留最近${PACCACHE_KEEP}个版本）..."
  local pacman_cache_output
  # `paccache` 需要 root 权限来访问 /var/cache/pacman/pkg/。
  pacman_cache_output=$( { run_sudo paccache -rk${PACCACHE_KEEP} && run_sudo paccache -ruk0; } 2>&1 )
  local freed_space="0MB"
  # 如果 `bc` 命令可用，则精确计算清理的空间大小。
  if [[ "${COMMAND_AVAILABLE[bc]}" == "true" && -n "$pacman_cache_output" ]]; then
    local total_freed_mb=$(echo "scale=2; $(
      echo "$pacman_cache_output" | grep -oE '[0-9]+\.?[0-9]*\s+(GiB|MiB|KiB)' | while IFS=' ' read -r size unit; do
        case "$unit" in
          "GiB") echo -n "$size * 1024 + " ;;
          "MiB") echo -n "$size + " ;;
          "KiB") echo -n "$size / 1024 + " ;;
        esac
      done
      echo "0"
    )" | bc)
    freed_space="$(printf "%.0fMB" "$total_freed_mb")"
  elif [[ -n "$pacman_cache_output" ]]; then
    freed_space="已清理" # 如果 bc 不可用，则给出一个通用状态。
  fi
  SCRIPT_STATS[pacman_cache_freed]="$freed_space"
  log_success "Pacman包缓存清理完成"

  # 清理 Paru 构建缓存。
  log "清理Paru构建缓存..."
  if [[ -d "$PARU_CACHE_DIR" ]]; then
    # 这是用户家目录下的文件，不需要 root 权限。
    if find "$PARU_CACHE_DIR" -mindepth 1 -delete 2>/dev/null; then
      SCRIPT_STATS[paru_cache_status]="清理成功"; log_success "Paru构建缓存清理完成"
    else
      SCRIPT_STATS[paru_cache_status]="清理失败"; log_warning "Paru构建缓存清理失败"
    fi
  else
    SCRIPT_STATS[paru_cache_status]="无需清理"; log "未找到Paru构建缓存目录"
  fi

  # 清理系统和用户级别的临时文件。
  if [[ "$ENABLE_SYSTEM_CLEANUP" == "true" ]]; then
    log "清理系统临时文件..."
    local cleaned_size=0
    # 清理用户缩略图缓存，不需要 root。
    if [[ -d "$HOME/.cache/thumbnails" ]]; then
      local thumb_size=$(du -sb "$HOME/.cache/thumbnails" 2>/dev/null | cut -f1 || echo 0)
      # `(N)` 是 zsh 的 glob qualifier，当没有匹配项时不会报错，而是展开为空列表。
      rm -rf "$HOME/.cache/thumbnails"/*(N)
      cleaned_size=$((cleaned_size + thumb_size))
    fi
    # 清理系统日志和临时目录，需要 root。
    run_sudo find /var/log -name "*.log.*" -mtime +30 -delete 2>/dev/null || true
    run_sudo find /tmp -type f -atime +7 -delete 2>/dev/null || true
    # 清理用户 Xorg 日志，不需要 root。
    find "$HOME/.local/share/xorg" -name "*.log.*" -mtime +7 -delete 2>/dev/null || true
    local cleaned_mb=$((cleaned_size / 1024 / 1024))
    SCRIPT_STATS[system_cleanup]="${cleaned_mb}MB"
    log_success "系统清理完成: ${cleaned_mb}MB"
  fi
  return 0
}

# 步骤4：并行更新其他组件。
run_step_component_updates() {
  show_progress "组件更新"
  local update_tasks=()
  # 根据命令是否存在，动态构建要执行的任务列表。
  [[ "${COMMAND_AVAILABLE[flatpak]}" == "true" ]] && update_tasks+=("update_flatpak")
  [[ "${COMMAND_AVAILABLE[xmake]}" == "true" ]] && update_tasks+=("update_xmake")
  [[ "${COMMAND_AVAILABLE[zinit]}" == "true" ]] && update_tasks+=("update_zinit")

  if (( ${#update_tasks[@]} > 0 )); then
    if [[ "$PARALLEL_MODE" == "true" ]]; then
      log "并行更新组件..."
      run_parallel "${update_tasks[@]}"
    else
      log "串行更新组件..."
      for task in "${update_tasks[@]}"; do $task; done
    fi
    # 收集并行任务的结果。
    for task in "${update_tasks[@]}"; do
      local component_name=${task#update_}
      local status_file="$temp_dir/${component_name}_status"
      if [[ -f "$status_file" ]]; then
        SCRIPT_STATS[${component_name}_status]=$(<"$status_file")
      fi
    done
  else
    log "没有可更新的额外组件。"
  fi
  [[ "${COMMAND_AVAILABLE[go]}" == "true" ]] && { log "清理Go缓存..."; cleanup_go; }
  return 0
}

# 步骤5：收尾任务。
run_step_final_tasks() {
  show_progress "收尾任务"
  # 如果图标修复脚本存在且可执行，则运行它。
  if [[ -x "$ICON_SCRIPT" ]]; then
    log "执行图标修复脚本..."
    # 建议让 ICON_SCRIPT 内部需要 root 权限的命令自己调用 sudo，而不是用 sudo 运行整个脚本。
    # 这遵循了“最小权限原则”。
    if "$ICON_SCRIPT" &>/dev/null; then
      SCRIPT_STATS[icon_status]="成功"; log_success "图标修复完成"
    else
      SCRIPT_STATS[icon_status]="失败"; log_warning "图标修复脚本执行失败"
    fi
  else
    SCRIPT_STATS[icon_status]="跳过"
  fi
  return 0
}

# ========================
# 主流程定义
# ========================
# 定义主流程中的所有步骤函数。
declare -a main_steps
main_steps=(
  "run_step_pre_checks"
  "run_step_system_update"
  "run_step_cleanup"
  "run_step_component_updates"
  "run_step_final_tasks"
)
typeset -gr TOTAL_STEPS=${#main_steps[@]} # 计算总步骤数，用于进度显示。

# 主函数入口。
main() {
  # 创建一个临时目录，并使用 `trap` 确保脚本在任何情况下退出（正常、中断、终止）时都会被删除。
  temp_dir=$(mktemp -d)
  trap "rm -rf '$temp_dir'" EXIT INT TERM

  # 初始化统计数据。
  SCRIPT_STATS=(
    start_time $(date +%s)
    warnings 0 errors 0
    orphans_cleaned "未执行" pacman_cache_freed "0MB"
    paru_cache_status "未执行" system_cleanup "0MB"
    flatpak_status "跳过" xmake_status "跳过"
    zinit_status "跳过" go_status "跳过" icon_status "跳过"
  )
  echo -e "${BOLD}${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
  echo -e "${BOLD}${CYAN}║                    🚀 Arch Linux 每日更新                    ║${NC}"
  echo -e "${BOLD}${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
  echo

  # 按顺序执行定义好的每个步骤。
  for step_func in "${main_steps[@]}"; do
    # 如果任何一个关键步骤失败，则立即终止脚本，并打印摘要。
    if ! $step_func; then
      log_error "关键步骤 '$step_func' 失败，脚本终止。"
      print_summary
      exit 1
    fi
  done

  log_success "所有任务已完成！"
  print_summary
}

# 使用 "$@" 将所有命令行参数传递给 main 函数。
main "$@"
