#!/usr/bin/env zsh
set -euo pipefail # 启用严格模式，增强脚本健壮性

# ========================
# 常量与全局配置
# ========================
readonly PACMAN_LOCK="/var/lib/pacman/db.lck"
readonly ICON_SCRIPT="$HOME/APP/Scripts/fix_app_icons/fix_app_icons.sh"
readonly PACCACHE_KEEP=2
readonly ORPHAN_CLEANUP_DAY="Mon"
readonly PARU_CACHE_DIR="$HOME/.cache/paru/clone"
readonly TEMP_DIRS=("/tmp" "/var/tmp" "$HOME/.cache/thumbnails")
readonly LOG_DIRS=("/var/log" "$HOME/.local/share/xorg")

# 命令行参数
VERBOSE_MODE=true
PARALLEL_MODE=true
ENABLE_SYSTEM_CLEANUP=true

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  case $1 in
    -q|--quiet) VERBOSE_MODE=false; shift ;;
    --no-parallel) PARALLEL_MODE=false; shift ;;
    --no-cleanup) ENABLE_SYSTEM_CLEANUP=false; shift ;;
    -h|--help)
      echo "用法: $0 [选项]"
      echo "选项:"
      echo "  -q, --quiet      简洁模式输出"
      echo "  --no-parallel    禁用并行处理"
      echo "  --no-cleanup     禁用系统清理"
      echo "  -h, --help       显示此帮助信息"
      exit 0 ;;
    *) echo "未知选项: $1"; exit 1 ;;
  esac
done

# 全局统计信息数组
typeset -gA SCRIPT_STATS
typeset -gA COMMAND_AVAILABLE

# 总步骤数（用于进度显示）
readonly TOTAL_STEPS=12

# ========================
# 颜色定义与日志函数
# ========================
if [[ -t 1 ]]; then
  readonly GREEN='\033[0;32m'
  readonly RED='\033[0;31m'
  readonly YELLOW='\033[0;33m'
  readonly BLUE='\033[0;34m'
  readonly CYAN='\033[0;36m'
  readonly BOLD='\033[1m'
  readonly NC='\033[0m'
  readonly CHECKMARK='✓'
  readonly CROSSMARK='✗'
  readonly ARROW='→'
  readonly GEAR='⚙'
else
  readonly GREEN=''
  readonly RED=''
  readonly YELLOW=''
  readonly BLUE=''
  readonly CYAN=''
  readonly BOLD=''
  readonly NC=''
  readonly CHECKMARK='[OK]'
  readonly CROSSMARK='[FAIL]'
  readonly ARROW='->'
  readonly GEAR='[WORK]'
fi

# 当前步骤计数器
CURRENT_STEP=0

log_time() { date +'%H:%M' }

# 进度显示函数
show_progress() {
  CURRENT_STEP=$((CURRENT_STEP + 1))
  local step_name="$1"
  printf "${BOLD}${CYAN}[%2d/%d]${NC} ${GEAR} %s\n" "$CURRENT_STEP" "$TOTAL_STEPS" "$step_name"
}

# 增强的日志函数
log() {
  [[ "$VERBOSE_MODE" == "true" ]] && echo -e "${BLUE}[$(log_time)] $*${NC}"
}

log_success() {
  echo -e "${GREEN}[$(log_time)] ${CHECKMARK} $*${NC}"
}

log_warning() {
  if [[ -v SCRIPT_STATS[warnings] ]]; then
    SCRIPT_STATS[warnings]=$((SCRIPT_STATS[warnings] + 1))
  fi
  echo -e "${YELLOW}[$(log_time)] ⚠ $*${NC}"
}

log_error() {
  if [[ -v SCRIPT_STATS[errors] ]]; then
    SCRIPT_STATS[errors]=$((SCRIPT_STATS[errors] + 1))
  fi
  echo -e "${RED}[$(log_time)] ${CROSSMARK} $*${NC}" >&2
}

# 进度条显示函数
show_spinner() {
  local pid=$1
  local message="$2"
  local spinner='⠋⠙⠹⠸⠼⠴⠦⠧⠇⠏'
  local i=0

  while kill -0 $pid 2>/dev/null; do
    printf "\r${BLUE}[$(log_time)] ${spinner:$i:1} %s${NC}" "$message"
    i=$(( (i+1) % ${#spinner} ))
    sleep 0.1
  done
  printf "\r"
}

# ========================
# 功能函数
# ========================

# 检查命令可用性并缓存结果
check_commands() {
  local commands=("fastfetch" "flatpak" "xmake" "zinit" "go" "curl" "paccache" "bc")
  for cmd in "${commands[@]}"; do
    if command -v "$cmd" &>/dev/null; then
      COMMAND_AVAILABLE[$cmd]=true
    else
      COMMAND_AVAILABLE[$cmd]=false
    fi
  done
}

# 增强的网络检查
check_network() {
  local endpoints=(
    "http://connectivitycheck.gstatic.com/generate_204"
    "http://www.msftconnecttest.com/connecttest.txt"
    "http://detectportal.firefox.com/success.txt"
  )

  for endpoint in "${endpoints[@]}"; do
    if curl -Is --connect-timeout 3 --max-time 5 "$endpoint" &>/dev/null; then
      return 0
    fi
  done
  return 1
}



run_with_retry() {
  if (( $# < 3 )); then
    log_error "run_with_retry 使用错误: 需要至少3个参数 (<重试次数> <延迟> <命令>...)"
    return 1
  fi
  local max_retries=$1
  local retry_delay=$2
  shift 2
  local cmd=("$@")
  local attempt=1

  while (( attempt <= max_retries )); do
    if "${cmd[@]}"; then
      return 0
    fi
    log_warning "命令 '${cmd[*]}' 尝试 $attempt/$max_retries 失败..."
    if (( attempt < max_retries )); then
      log_warning "将在 ${retry_delay} 秒后重试"
      sleep "$retry_delay"
    fi
    (( attempt++ ))
  done
  log_error "命令 '${cmd[*]}' 最终执行失败"
  return 1
}

# 磁盘空间检查
check_disk_space() {
  local min_free_gb=2
  local available_kb=$(df / | awk 'NR==2 {print $4}')
  local available_gb=$((available_kb / 1024 / 1024))

  if [[ $available_gb -lt $min_free_gb ]]; then
    log_warning "磁盘空间不足: ${available_gb}GB 可用 (建议至少 ${min_free_gb}GB)"
    return 1
  fi
  log "磁盘空间充足: ${available_gb}GB 可用"
  return 0
}

# 系统清理函数
cleanup_system() {
  [[ "$ENABLE_SYSTEM_CLEANUP" != "true" ]] && return 0

  log "清理系统临时文件..."
  local cleaned_size=0

  # 清理缩略图缓存
  if [[ -d "$HOME/.cache/thumbnails" ]]; then
    local thumb_size=$(du -sb "$HOME/.cache/thumbnails" 2>/dev/null | cut -f1 || echo 0)
    rm -rf "$HOME/.cache/thumbnails"/* 2>/dev/null || true
    cleaned_size=$((cleaned_size + thumb_size))
  fi

  # 清理旧的日志文件 (>30天)
  find /var/log -name "*.log.*" -mtime +30 -delete 2>/dev/null || true
  find "$HOME/.local/share/xorg" -name "*.log.*" -mtime +7 -delete 2>/dev/null || true

  # 清理临时文件
  find /tmp -type f -atime +7 -delete 2>/dev/null || true

  local cleaned_mb=$((cleaned_size / 1024 / 1024))
  SCRIPT_STATS[system_cleanup]="${cleaned_mb}MB 已清理"
  log_success "系统清理完成: ${cleaned_mb}MB"
}

should_clean_orphans() {
  [[ "$(date +'%a')" == "$ORPHAN_CLEANUP_DAY" ]]
}

clean_orphans() {
  log "检查孤儿包..."
  local orphans
  orphans=($(pacman -Qtdq 2>/dev/null || true))
  if (( #orphans > 0 )); then
    SCRIPT_STATS[orphans_cleaned]=$#orphans
    log_warning "发现 $#orphans 个孤儿包"
    log "清理孤儿包..."
    if sudo pacman -Rns --noconfirm "${orphans[@]}" &>/dev/null; then
      log_success "孤儿包清理完成"
    else
      log_error "孤儿包清理失败"
      SCRIPT_STATS[orphans_cleaned]=0
    fi
  else
    SCRIPT_STATS[orphans_cleaned]=0
    log_success "未发现孤儿包"
  fi
}

# 并行执行函数
run_parallel() {
  local pids=()
  local commands=("$@")
  local results=()

  # 启动所有后台任务
  for cmd in "${commands[@]}"; do
    eval "$cmd" &
    pids+=($!)
  done

  # 等待所有任务完成
  for pid in "${pids[@]}"; do
    if wait $pid; then
      results+=(0)
    else
      results+=(1)
    fi
  done

  # 返回是否所有任务都成功
  for result in "${results[@]}"; do
    [[ $result -eq 0 ]] || return 1
  done
  return 0
}

# 格式化状态的辅助函数
format_status() {
  case "$1" in
    *"成功"*|*"已执行"*|*"清理成功"*|*"无需清理"*|*"个"*)
      echo -e "${GREEN}$1${NC}" ;;
    *"失败"*|*"清理失败"*)
      echo -e "${RED}$1${NC}" ;;
    *)
      echo -e "${YELLOW}$1${NC}" ;;
  esac
}

# 辅助函数：格式化表格行
format_table_row() {
  local label="$1"
  local value="$2"
  local total_width=58  # 总宽度减去边框字符

  # 移除颜色代码来计算实际长度
  local clean_label=$(echo "$label" | sed 's/\x1b\[[0-9;]*m//g')
  local clean_value=$(echo "$value" | sed 's/\x1b\[[0-9;]*m//g')
  local label_len=${#clean_label}
  local value_len=${#clean_value}
  local spaces_needed=$((total_width - label_len - value_len))

  # 确保至少有1个空格
  if [[ $spaces_needed -lt 1 ]]; then
    spaces_needed=1
  fi

  printf "${CYAN}║${NC} %s%*s%s ${CYAN}║${NC}\n" "$label" "$spaces_needed" "" "$value"
}

# 计算字符显示宽度（处理emoji组合字符）
calculate_display_width() {
  local text="$1"

  # 移除颜色代码
  local clean_text=$(echo "$text" | sed -E 's/\x1b\[[0-9;]*m//g')

  # 先移除变体选择符 (U+FE0F)，这样emoji组合字符就会被正确计算
  clean_text=$(echo "$clean_text" | sed 's/️//g')

  # 计算实际显示宽度
  local width=0
  local i

  for ((i=0; i<${#clean_text}; i++)); do
    local char="${clean_text:$i:1}"
    local unicode_val=$(printf "%d" "'$char" 2>/dev/null || echo "0")

    # 检查是否为中文字符或emoji（Unicode范围）
    if [[ $unicode_val -gt 127 ]]; then
      width=$((width + 2))  # 中文字符和emoji占2个单位宽度
    else
      width=$((width + 1))  # 英文字符占1个单位宽度
    fi
  done

  echo $width
}

# 辅助函数：打印对齐的表格行（基于实际字符宽度）
print_aligned_row() {
  local content="$1"
  local target_width=60  # 目标内容宽度

  # 计算实际显示宽度
  local actual_width=$(calculate_display_width "$content")

  # 计算需要的填充空格数
  local padding=$((target_width - actual_width))

  # 确保填充不为负数
  if [[ $padding -lt 0 ]]; then
    padding=0
  fi

  # 生成填充空格
  local spaces=""
  for ((i=0; i<padding; i++)); do
    spaces+=" "
  done

  echo -e "${CYAN}║${NC} ${content}${spaces} ${CYAN}║${NC}"
}

# 简化的摘要显示
print_summary() {
  local end_time=$(date +%s)
  local duration=$((end_time - SCRIPT_STATS[start_time]))
  local minutes=$((duration / 60))
  local seconds=$((duration % 60))

  echo
  echo -e "${BOLD}${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
  echo -e "${BOLD}${CYAN}║                        📊 执行摘要                           ║${NC}"
  echo -e "${BOLD}${CYAN}╠══════════════════════════════════════════════════════════════╣${NC}"

  # 基本信息
  print_aligned_row "⏱️  总耗时: ${GREEN}${minutes}分${seconds}秒${NC}"
  print_aligned_row "⚠️  警告: ${YELLOW}${SCRIPT_STATS[warnings]}${NC} | ❌ 错误: ${RED}${SCRIPT_STATS[errors]}${NC}"

  echo -e "${CYAN}╠══════════════════════════════════════════════════════════════╣${NC}"
  echo -e "${CYAN}║${NC} 🧹 清理与维护                                                ${CYAN}║${NC}"
  echo -e "${CYAN}╠══════════════════════════════════════════════════════════════╣${NC}"

  # 清理信息
  print_aligned_row "  孤儿包: $(format_status "${SCRIPT_STATS[orphans_cleaned]} 个")"
  print_aligned_row "  Pacman缓存: $(format_status "${SCRIPT_STATS[pacman_cache_freed]}")"
  print_aligned_row "  Paru缓存: $(format_status "${SCRIPT_STATS[paru_cache_status]}")"
  print_aligned_row "  系统清理: $(format_status "${SCRIPT_STATS[system_cleanup]}")"

  echo -e "${CYAN}╠══════════════════════════════════════════════════════════════╣${NC}"
  echo -e "${CYAN}║${NC} 🔄 组件更新                                                  ${CYAN}║${NC}"
  echo -e "${CYAN}╠══════════════════════════════════════════════════════════════╣${NC}"

  # 组件更新信息
  print_aligned_row "  Flatpak: $(format_status "${SCRIPT_STATS[flatpak_status]}")"
  print_aligned_row "  Xmake: $(format_status "${SCRIPT_STATS[xmake_status]}")"
  print_aligned_row "  Zinit: $(format_status "${SCRIPT_STATS[zinit_status]}")"
  print_aligned_row "  图标修复: $(format_status "${SCRIPT_STATS[icon_status]}")"

  echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
  echo
}

# ========================
# 主流程
# ========================
main() {
  # 初始化统计信息
  SCRIPT_STATS[start_time]=$(date +%s)
  SCRIPT_STATS[orphans_cleaned]=0
  SCRIPT_STATS[pacman_cache_freed]="未执行"
  SCRIPT_STATS[paru_cache_status]="未执行"
  SCRIPT_STATS[system_cleanup]="未执行"
  SCRIPT_STATS[warnings]=0
  SCRIPT_STATS[errors]=0
  SCRIPT_STATS[flatpak_status]="跳过"
  SCRIPT_STATS[xmake_status]="跳过"
  SCRIPT_STATS[zinit_status]="跳过"
  SCRIPT_STATS[go_status]="跳过"
  SCRIPT_STATS[icon_status]="跳过"

  # 显示脚本标题
  echo -e "${BOLD}${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
  echo -e "${BOLD}${CYAN}║                    🚀 Arch Linux 每日更新                    ║${NC}"
  echo -e "${BOLD}${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
  echo

  # 步骤1: 权限检查
  show_progress "权限检查"
  if (( EUID == 0 )); then
    log_error "请不要以 root 用户身份直接运行此脚本"
    exit 1
  fi

  # 步骤2: 检查命令可用性
  show_progress "检查系统组件"
  check_commands

  # 步骤3: 系统信息显示
  show_progress "显示系统信息"
  if [[ "${COMMAND_AVAILABLE[fastfetch]}" == "true" ]]; then
    fastfetch
  else
    log_warning "未找到 fastfetch，跳过系统信息显示"
  fi

  # 步骤4: 网络连接检查
  show_progress "网络连接检查"
  if ! run_with_retry 2 2 check_network; then
    log_error "网络连接异常，脚本终止"
    exit 1
  fi
  log_success "网络连接正常"

  # 步骤5: 磁盘空间检查
  show_progress "磁盘空间检查"
  check_disk_space

  # 步骤6: sudo权限验证
  show_progress "权限验证"
  if ! sudo -v; then
    log_error "无法获取sudo权限，脚本终止"
    exit 1
  fi
  log_success "sudo权限验证通过"

  # 步骤7: 系统进程锁检查
  show_progress "系统锁检查"
  if pgrep -x "pacman|paru" >/dev/null; then
    log_error "检测到正在运行的 pacman/paru 进程，脚本终止"
    exit 1
  elif [[ -f $PACMAN_LOCK ]]; then
    log_warning "发现残留锁文件，尝试清理..."
    if ! sudo rm -v "$PACMAN_LOCK"; then
        log_error "锁文件清理失败，请手动删除 $PACMAN_LOCK"
        exit 1
    fi
    log_success "锁文件清理完成"
  fi

  # 步骤8: 系统更新
  show_progress "Arch系统更新"
  log "开始系统更新..."
  if ! run_with_retry 3 10 paru -Syu --noconfirm --needed --skipreview; then
    log_error "系统更新失败，脚本终止"
    exit 1
  fi
  log_success "系统更新完成"

  # 步骤9: 孤儿包清理
  show_progress "孤儿包清理"
  if should_clean_orphans; then
    log "今天是 $ORPHAN_CLEANUP_DAY，执行孤儿包清理"
    clean_orphans
  else
    log "跳过孤儿包清理（非预定日）"
  fi

  # 步骤10: 缓存清理
  show_progress "缓存清理"

  # 清理损坏的pacman临时目录
  log "清理损坏的pacman临时下载目录..."
  sudo find /var/cache/pacman/pkg/ -name "download-*" -type d -prune -exec rm -rf {} + 2>/dev/null || true

  # Pacman缓存清理
  log "清理pacman包缓存（保留最近${PACCACHE_KEEP}个版本）..."
  local pacman_cache_output
  pacman_cache_output=$( { sudo paccache -rk${PACCACHE_KEEP} && sudo paccache -ruk0; } 2>&1 )

  # 更准确的空间计算
  local freed_space="0B"
  if [[ -n "$pacman_cache_output" ]]; then
    local total_freed_mb=0
    while IFS= read -r line; do
      if [[ "$line" =~ ([0-9]+\.?[0-9]*)[[:space:]]+(MiB|GiB|KiB|B) ]]; then
        # 使用 Zsh 原生的 `match` 数组，而非 Bash 的 `BASH_REMATCH`
        local size="${match[1]}"
        local unit="${match[2]}"
        local size_int=${size%.*}  # 取整数部分
        case "$unit" in
          "GiB") total_freed_mb=$((total_freed_mb + size_int * 1024)) ;;
          "MiB") total_freed_mb=$((total_freed_mb + size_int)) ;;
          "KiB") total_freed_mb=$((total_freed_mb + size_int / 1024)) ;;
        esac
      fi
    done <<< "$pacman_cache_output"

    if [[ $total_freed_mb -gt 0 ]]; then
      freed_space="${total_freed_mb}MB"
    fi
  fi
  SCRIPT_STATS[pacman_cache_freed]="$freed_space"
  log_success "Pacman包缓存清理完成"

  # Paru缓存清理
  log "清理Paru构建缓存..."
  if [[ -d "$PARU_CACHE_DIR" ]]; then
    if find "$PARU_CACHE_DIR" -mindepth 1 -delete 2>/dev/null; then
        SCRIPT_STATS[paru_cache_status]="清理成功"
        log_success "Paru构建缓存清理完成"
    else
        SCRIPT_STATS[paru_cache_status]="清理失败"
        log_warning "Paru构建缓存清理失败"
    fi
  else
    SCRIPT_STATS[paru_cache_status]="无需清理"
    log_success "未找到Paru构建缓存目录"
  fi

  # 系统清理
  cleanup_system

  # 步骤11: 组件更新（并行处理）
  show_progress "组件更新"

  if [[ "$PARALLEL_MODE" == "true" ]]; then
    log "并行更新组件..."
    local parallel_commands=()

    # 准备并行命令
    if [[ "${COMMAND_AVAILABLE[flatpak]}" == "true" ]]; then
      parallel_commands+=("update_flatpak")
    fi

    if [[ "${COMMAND_AVAILABLE[xmake]}" == "true" ]]; then
      parallel_commands+=("update_xmake")
    fi

    if [[ "${COMMAND_AVAILABLE[zinit]}" == "true" ]]; then
      parallel_commands+=("update_zinit")
    fi

    # 执行并行更新
    if [[ ${#parallel_commands[@]} -gt 0 ]]; then
      run_parallel "${parallel_commands[@]}"
    fi
  else
    # 串行更新
    [[ "${COMMAND_AVAILABLE[flatpak]}" == "true" ]] && update_flatpak
    [[ "${COMMAND_AVAILABLE[xmake]}" == "true" ]] && update_xmake
    [[ "${COMMAND_AVAILABLE[zinit]}" == "true" ]] && update_zinit
  fi

  # Go缓存清理
  if [[ "${COMMAND_AVAILABLE[go]}" == "true" ]]; then
    log "清理Go缓存..."
    cleanup_go
  fi

  # 步骤12: 图标修复
  show_progress "图标修复"
  if [[ -x "$ICON_SCRIPT" ]]; then
    log "执行图标修复脚本..."
    if sudo "$ICON_SCRIPT" &>/dev/null; then
      SCRIPT_STATS[icon_status]="成功"
      log_success "图标修复完成"
    else
      SCRIPT_STATS[icon_status]="失败"
      log_warning "图标修复脚本执行失败"
    fi
  fi

  log_success "所有任务已完成！"
  print_summary
}

# 组件更新函数
update_flatpak() {
  if run_with_retry 3 5 flatpak update -y &>/dev/null; then
    SCRIPT_STATS[flatpak_status]="成功"
    log_success "Flatpak更新完成"
  else
    SCRIPT_STATS[flatpak_status]="失败"
    log_warning "Flatpak更新失败"
  fi
}

update_xmake() {
  if run_with_retry 2 5 xmake repo -u &>/dev/null; then
    SCRIPT_STATS[xmake_status]="成功"
    log_success "Xmake更新完成"
  else
    SCRIPT_STATS[xmake_status]="失败"
    log_warning "Xmake更新失败"
  fi
}

update_zinit() {
  if zsh -c 'source "/usr/share/zinit/zinit.zsh"; zinit update --parallel 160' &>/dev/null; then
    SCRIPT_STATS[zinit_status]="成功"
    log_success "Zinit更新完成"
  else
    SCRIPT_STATS[zinit_status]="失败"
    log_warning "Zinit更新失败"
  fi
}

cleanup_go() {
  # 清理Go缓存
  go clean -cache -modcache -testcache -fuzzcache 2>/dev/null || go clean -cache -modcache -testcache

  # 简化的Go目录清理逻辑
  if [[ -d "$HOME/go" ]]; then
    local go_size=$(du -sb "$HOME/go" 2>/dev/null | cut -f1 || echo 0)
    local go_files=$(find "$HOME/go" -type f 2>/dev/null | wc -l)

    # 如果目录很小且文件很少，可能是空目录
    if [[ $go_size -lt 1048576 && $go_files -lt 10 ]]; then  # <1MB 且 <10个文件
      log_warning "检测到可能为空的 \$HOME/go 目录，执行清理"
      rm -rf "$HOME/go"
      log_success "已清理空的 \$HOME/go 目录"
    else
      log "保留 \$HOME/go 目录（包含有效内容）"
    fi
  fi

  SCRIPT_STATS[go_status]="已执行"
  log_success "Go缓存清理完成"
}

main "$@"
